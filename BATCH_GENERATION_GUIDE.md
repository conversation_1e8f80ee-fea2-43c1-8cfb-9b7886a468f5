# 批量评语生成功能使用指南

## 功能概述

批量评语生成功能允许用户一次性为多个学生生成个性化评语，大大提高了工作效率，特别适合班主任需要为整个班级生成期末评语的场景。

## 主要功能特性

### 1. 多种学生信息输入方式
- **手动添加**: 逐个添加学生信息
- **批量添加**: 通过文本框批量输入学生姓名
- **Excel导入**: 支持从Excel文件导入学生信息
- **模板下载**: 提供标准Excel模板下载

### 2. 全局设置功能
- **评语语气**: 温和亲切、严谨正式、鼓励激励、客观中性
- **评语字数**: 150-200字、200-300字、300-400字、400-500字
- **评语人称**: "你" 或 "该生"
- **一键应用**: 将全局设置应用到所有学生

### 3. 智能批量处理
- **队列式生成**: 避免API并发限制，逐个生成评语
- **实时进度**: 显示生成进度和状态
- **错误处理**: 自动重试失败的生成任务
- **结果管理**: 统一管理所有生成结果

### 4. 结果展示与管理
- **实时状态**: 显示每个学生的生成状态
- **详细结果**: 展示生成的评语内容
- **批量操作**: 复制所有评语、单个复制
- **历史保存**: 自动保存到历史记录

## 使用流程

### 步骤1: 访问批量生成页面
1. 登录系统后，点击顶部导航的"批量生成"按钮
2. 进入批量评语生成页面

### 步骤2: 设置全局参数
1. 在"全局设置"区域配置：
   - 评语语气（默认：温和亲切）
   - 评语字数范围（默认：200-300字）
   - 评语人称（默认：你）
2. 点击"应用到所有学生"按钮

### 步骤3: 添加学生信息
选择以下任一方式添加学生：

#### 方式A: 手动添加
1. 点击"添加学生"按钮
2. 填写学生基本信息：
   - 学生姓名（必填）
   - 学生性别
   - 班干职位
   - 特长与兴趣
   - 获奖情况

#### 方式B: 批量添加
1. 点击"批量添加"按钮
2. 在弹出框中每行输入一个学生姓名
3. 点击"添加"按钮

#### 方式C: Excel导入
1. 点击"下载模板"获取标准Excel模板
2. 按模板格式填写学生信息
3. 点击"Excel导入"选择文件上传

### 步骤4: 开始批量生成
1. 确认学生信息无误
2. 点击"开始批量生成"按钮
3. 系统自动跳转到状态页面

### 步骤5: 查看生成结果
1. 在状态页面实时查看生成进度
2. 查看每个学生的生成状态和结果
3. 复制需要的评语内容
4. 所有评语自动保存到历史记录

## Excel模板格式

| 学生姓名 | 性别 | 班干职位 | 特长与兴趣 | 获奖情况 |
|---------|------|----------|------------|----------|
| 张三    | 男   | 班长     | 爱好绘画，作品有创意 | 校三好学生、数学竞赛二等奖 |
| 李四    | 女   | 学习委员 | 篮球特长，是校队成员 | 优秀班干部、英语演讲比赛一等奖 |

## 技术特性

### 1. 性能优化
- **分批处理**: 每次最多处理50个学生
- **队列机制**: 避免API并发限制
- **延迟控制**: 生成间隔1秒，保护API稳定性

### 2. 错误处理
- **数据验证**: 上传前验证必填字段
- **格式检查**: 检查Excel格式和数据类型
- **生成重试**: 自动重试失败的生成任务
- **部分成功**: 允许导出已成功生成的评语

### 3. 安全保障
- **用户隔离**: 每个用户只能访问自己的批量任务
- **权限控制**: 基于JWT的身份验证
- **数据保护**: 行级安全策略(RLS)保护数据

### 4. 数据管理
- **任务追踪**: 完整的批量任务生命周期管理
- **结果存储**: 详细的生成结果和错误信息
- **历史记录**: 自动保存到个人历史记录
- **Token统计**: 准确记录Token使用情况

## 注意事项

1. **学生数量限制**: 单次批量生成最多支持50个学生
2. **必填字段**: 学生姓名为必填项，其他字段可选
3. **生成时间**: 根据学生数量，完整生成可能需要几分钟
4. **网络稳定**: 确保网络连接稳定，避免生成中断
5. **浏览器保持**: 生成过程中请保持浏览器页面打开

## 故障排除

### 常见问题

**Q: 批量生成失败怎么办？**
A: 检查网络连接，确认学生姓名已填写，可以重新启动批量生成。

**Q: Excel导入失败？**
A: 确保使用提供的模板格式，检查文件是否为.xlsx或.xls格式。

**Q: 部分学生生成失败？**
A: 系统会显示具体错误信息，可以针对失败的学生重新生成。

**Q: 如何查看历史批量生成记录？**
A: 所有生成的评语会自动保存到"历史评语"页面，可以随时查看。

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础批量生成功能
- ✅ 多种学生信息输入方式
- ✅ 全局设置和个性化配置
- ✅ 实时进度显示
- ✅ Excel导入导出功能
- ✅ 错误处理和重试机制

### 计划功能
- 🔄 断点续传功能
- 🔄 批量编辑和快速操作
- 🔄 更多导出格式（Word、PDF）
- 🔄 批量生成模板保存
