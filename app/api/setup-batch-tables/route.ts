import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('开始设置批量生成表...');

    // 删除现有表（如果存在问题）
    const dropSql = `
      DROP TABLE IF EXISTS batch_generation_results CASCADE;
      DROP TABLE IF EXISTS batch_generation_tasks CASCADE;
    `;

    // 创建批量生成任务表
    const createTaskTableSql = `
      CREATE TABLE batch_generation_tasks (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          total_students INTEGER NOT NULL,
          completed_count INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE
      );
    `;

    // 创建批量生成结果表
    const createResultTableSql = `
      CREATE TABLE batch_generation_results (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          batch_task_id UUID NOT NULL REFERENCES batch_generation_tasks(id) ON DELETE CASCADE,
          student_name VARCHAR(100) NOT NULL,
          student_info JSONB NOT NULL,
          generated_comment TEXT,
          tokens_used INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'success', 'failed')),
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // 创建索引
    const createIndexesSql = `
      CREATE INDEX idx_batch_tasks_user_id ON batch_generation_tasks(user_id);
      CREATE INDEX idx_batch_tasks_status ON batch_generation_tasks(status);
      CREATE INDEX idx_batch_tasks_created_at ON batch_generation_tasks(created_at);
      
      CREATE INDEX idx_batch_results_task_id ON batch_generation_results(batch_task_id);
      CREATE INDEX idx_batch_results_status ON batch_generation_results(status);
      CREATE INDEX idx_batch_results_student_name ON batch_generation_results(student_name);
    `;

    // 启用RLS
    const enableRlsSql = `
      ALTER TABLE batch_generation_tasks ENABLE ROW LEVEL SECURITY;
      ALTER TABLE batch_generation_results ENABLE ROW LEVEL SECURITY;
    `;

    // 创建RLS策略
    const createPoliciesSql = `
      CREATE POLICY "Users can view own batch tasks" ON batch_generation_tasks
          FOR SELECT USING (auth.uid() = user_id);
      
      CREATE POLICY "Service can manage batch tasks" ON batch_generation_tasks
          FOR ALL WITH CHECK (true);
      
      CREATE POLICY "Users can view own batch results" ON batch_generation_results
          FOR SELECT USING (
              batch_task_id IN (
                  SELECT id FROM batch_generation_tasks WHERE auth.uid() = user_id
              )
          );
      
      CREATE POLICY "Service can manage batch results" ON batch_generation_results
          FOR ALL WITH CHECK (true);
    `;

    // 执行SQL
    try {
      // 删除现有表
      const { error: dropError } = await supabaseAdmin.rpc('exec_sql', { sql: dropSql });
      if (dropError) console.log('删除表时的警告:', dropError);

      // 创建任务表
      const { error: taskError } = await supabaseAdmin.rpc('exec_sql', { sql: createTaskTableSql });
      if (taskError) throw taskError;

      // 创建结果表
      const { error: resultError } = await supabaseAdmin.rpc('exec_sql', { sql: createResultTableSql });
      if (resultError) throw resultError;

      // 创建索引
      const { error: indexError } = await supabaseAdmin.rpc('exec_sql', { sql: createIndexesSql });
      if (indexError) console.log('创建索引时的警告:', indexError);

      // 启用RLS
      const { error: rlsError } = await supabaseAdmin.rpc('exec_sql', { sql: enableRlsSql });
      if (rlsError) console.log('启用RLS时的警告:', rlsError);

      // 创建策略
      const { error: policyError } = await supabaseAdmin.rpc('exec_sql', { sql: createPoliciesSql });
      if (policyError) console.log('创建策略时的警告:', policyError);

      return NextResponse.json({
        success: true,
        message: '批量生成表创建成功'
      });

    } catch (sqlError) {
      console.error('SQL执行失败:', sqlError);
      
      // 如果SQL执行失败，返回手动执行的SQL
      return NextResponse.json({
        success: false,
        message: '自动创建失败，请手动执行SQL',
        sql: dropSql + createTaskTableSql + createResultTableSql + createIndexesSql + enableRlsSql + createPoliciesSql
      });
    }

  } catch (error) {
    console.error('设置批量生成表错误:', error);
    return NextResponse.json({
      success: false,
      message: '设置失败',
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}
